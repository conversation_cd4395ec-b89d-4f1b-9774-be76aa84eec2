# Firestore 404 Document Update Error Fix

## Problem Summary

**Error Message:** `404 No document to update: projects/drcr-d660a/databases/(default)/documents/TRANSACTIONS/0d3744bf-045f-49ba-afb5-8c71bc4984a3`

**Root Cause:** The Xero sync consumer cloud function was attempting to update TRANSACTIONS documents that didn't exist yet in Firestore. This created a race condition where:

1. Bills are processed and added to a batch using `batch.set()` with `merge=True`
2. Prepayment processing runs within the same loop before the batch is committed
3. The prepayment processing tries to update the TRANSACTIONS document with `update()`
4. The document doesn't exist yet because the batch hasn't been committed
5. Firestore returns a 404 error: "No document to update"

## Locations Fixed

### 1. Amortization Schedule Creation (Line 1042-1046)
**File:** `cloud_functions/xero_sync_consumer/main.py`
**Function:** `_generate_and_save_amortization_schedule()`

**Before:**
```python
# Update parent transaction (this part assumes TRANSACTIONS is top-level)
parent_transaction_ref = db.collection("TRANSACTIONS").document(invoice_id)
await parent_transaction_ref.update({
    "_system_amortizationScheduleIDs": firestore.ArrayUnion([schedule_id_val]),
    "updated_at": firestore.SERVER_TIMESTAMP
})
```

**After:**
```python
# Update parent transaction with existence check
parent_transaction_ref = db.collection("TRANSACTIONS").document(invoice_id)
try:
    # First, check if the document exists
    parent_doc = await parent_transaction_ref.get()
    if parent_doc.exists:
        # Document exists, use update
        await parent_transaction_ref.update({
            "_system_amortizationScheduleIDs": firestore.ArrayUnion([schedule_id_val]),
            "updated_at": firestore.SERVER_TIMESTAMP
        })
        logger.info(f"Successfully updated existing TRANSACTIONS document {invoice_id}")
    else:
        # Document doesn't exist, use set with merge=True to create it
        logger.warning(f"TRANSACTIONS document {invoice_id} does not exist yet. Creating with amortization schedule reference.")
        await parent_transaction_ref.set({
            "transaction_id": invoice_id,
            "entity_id": entity_id,
            "client_id": client_id,
            "_system_amortizationScheduleIDs": [schedule_id_val],
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        }, merge=True)
        logger.info(f"Successfully created TRANSACTIONS document {invoice_id}")
except Exception as e_transaction_update:
    logger.error(f"Failed to update/create TRANSACTIONS document {invoice_id}: {e_transaction_update}", exc_info=True)
    # Don't fail the entire schedule creation if transaction update fails
    logger.warning(f"Continuing with schedule creation despite transaction update failure for {invoice_id}")
```

### 2. Prepayment Analysis Update (Line 693-694)
**File:** `cloud_functions/xero_sync_consumer/main.py`
**Function:** `xero_sync_consumer()` - Bills sync section

**Before:**
```python
update_data = {"prepayment_analysis": combined_analysis, "has_amortization_schedules": combined_analysis["recommended_action"] == "create_amortization_schedule", "attachments_processed": len(processed_attachments), "llm_processing_completed": combined_analysis["llm_based_analysis_completed"]}
await transaction_ref.update(update_data)
```

**After:**
```python
update_data = {"prepayment_analysis": combined_analysis, "has_amortization_schedules": combined_analysis["recommended_action"] == "create_amortization_schedule", "attachments_processed": len(processed_attachments), "llm_processing_completed": combined_analysis["llm_based_analysis_completed"]}

# Use set with merge=True to handle cases where the document might not exist yet
try:
    # Check if document exists first
    doc_check = await transaction_ref.get()
    if doc_check.exists:
        await transaction_ref.update(update_data)
        logger.debug(f"Successfully updated TRANSACTIONS document {bill_id}")
    else:
        # Document doesn't exist, use set with merge=True
        logger.warning(f"TRANSACTIONS document {bill_id} does not exist yet. Using set with merge=True")
        update_data.update({
            "transaction_id": bill_id,
            "entity_id": entity_id,
            "client_id": client_id,
            "updated_at": firestore.SERVER_TIMESTAMP
        })
        await transaction_ref.set(update_data, merge=True)
        logger.debug(f"Successfully created/merged TRANSACTIONS document {bill_id}")
except Exception as e_update:
    logger.error(f"Failed to update TRANSACTIONS document {bill_id}: {e_update}", exc_info=True)
    # Store the error in the prepayment analysis for debugging
    update_data["prepayment_analysis"]["error"] = str(e_update)
    # Try one more time with set and merge=True as fallback
    try:
        update_data.update({
            "transaction_id": bill_id,
            "entity_id": entity_id,
            "client_id": client_id,
            "updated_at": firestore.SERVER_TIMESTAMP
        })
        await transaction_ref.set(update_data, merge=True)
        logger.info(f"Fallback: Successfully created TRANSACTIONS document {bill_id}")
    except Exception as e_fallback:
        logger.error(f"Fallback also failed for TRANSACTIONS document {bill_id}: {e_fallback}", exc_info=True)
```

### 3. Fallback Error Handling (Line 738-741)
**File:** `cloud_functions/xero_sync_consumer/main.py`
**Function:** `xero_sync_consumer()` - Bills sync error handling

**Before:**
```python
try:
    fallback_data = {"prepayment_analysis.gl_based_analysis_completed": True, "prepayment_analysis.llm_based_analysis_completed": False, "prepayment_analysis.has_prepayment_line_items": len(gl_prepayment_lines) > 0, "prepayment_analysis.error": str(e_prepayment)}
    ref_for_fallback = db.collection("TRANSACTIONS").document(current_bill_id)
    await ref_for_fallback.update(fallback_data)
    logger.info(f"Applied fallback for bill {current_bill_id}")
except Exception as e_fb: 
    logger.error(f"Failed fallback for bill {current_bill_id}: {e_fb}")
```

**After:**
```python
try:
    fallback_data = {"prepayment_analysis.gl_based_analysis_completed": True, "prepayment_analysis.llm_based_analysis_completed": False, "prepayment_analysis.has_prepayment_line_items": len(gl_prepayment_lines) > 0, "prepayment_analysis.error": str(e_prepayment)}
    ref_for_fallback = db.collection("TRANSACTIONS").document(current_bill_id)
    
    # Check if document exists before attempting update
    fallback_doc_check = await ref_for_fallback.get()
    if fallback_doc_check.exists:
        await ref_for_fallback.update(fallback_data)
        logger.info(f"Applied fallback update for existing bill {current_bill_id}")
    else:
        # Document doesn't exist, create it with fallback data using set with merge=True
        logger.warning(f"TRANSACTIONS document {current_bill_id} does not exist for fallback. Creating with minimal data.")
        fallback_create_data = {
            "transaction_id": current_bill_id,
            "entity_id": entity_id,
            "client_id": client_id,
            "prepayment_analysis": {
                "gl_based_analysis_completed": True,
                "llm_based_analysis_completed": False,
                "has_prepayment_line_items": len(gl_prepayment_lines) > 0,
                "error": str(e_prepayment)
            },
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        }
        await ref_for_fallback.set(fallback_create_data, merge=True)
        logger.info(f"Applied fallback creation for bill {current_bill_id}")
except Exception as e_fb: 
    logger.error(f"Failed fallback for bill {current_bill_id}: {e_fb}", exc_info=True)
```

### 4. Schedule Query Fix (Line 1275)
**File:** `cloud_functions/xero_sync_consumer/main.py`
**Function:** `_generate_proposed_journals_for_due_entries()`

**Before:**
```python
await schedules_query.document(schedule_id).update({"monthlyEntries": updated_monthly_entries, "updated_at": firestore.SERVER_TIMESTAMP})
```

**After:**
```python
# Fix: Use db.collection() instead of schedules_query.document()
schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(schedule_id)
await schedule_ref.update({"monthlyEntries": updated_monthly_entries, "updated_at": firestore.SERVER_TIMESTAMP})
```

## Solution Strategy

The fix implements a **defensive programming approach** with the following pattern:

1. **Check document existence** before attempting updates
2. **Use `set()` with `merge=True`** when document doesn't exist
3. **Use `update()`** when document exists
4. **Comprehensive error handling** with fallback mechanisms
5. **Detailed logging** for debugging and monitoring

## Benefits

1. **Eliminates 404 errors** by ensuring documents exist before updates
2. **Maintains data integrity** by creating documents with proper structure
3. **Provides graceful degradation** with fallback error handling
4. **Improves debugging** with detailed logging
5. **Prevents sync failures** from stopping the entire process

## Testing

The fix has been implemented and should resolve the specific error:
`404 No document to update: projects/drcr-d660a/databases/(default)/documents/TRANSACTIONS/0d3744bf-045f-49ba-afb5-8c71bc4984a3`

## Deployment

The changes are ready for deployment. The fix is backward compatible and will handle both existing and new scenarios gracefully.
