# Cloud Function specific (optional but good practice)
functions-framework==3.*
google-cloud-error-reporting>=1.5.2

# Core Dependencies for xero_sync_consumer
google-cloud-pubsub==2.29.0
google-cloud-secret-manager==2.23.3
httpx[http2]>=0.20.0
python-dotenv>=0.15.0
openai>=1.0.0 # If using OpenAI SDK later, or keep for consistency
Pillow>=9.0.0
opencv-python-headless>=4.5.0
numpy>=1.19.0
python-dateutil>=2.8.2
requests>=2.28.1 # Might be needed by dependencies

# Add later if needed:
google-cloud-storage==3.1.0
google-cloud-firestore
mistralai
python-jose[cryptography]>=3.3.0
cryptography>=3.4.8  # For token encryption in Firestore storage

uvicorn>=0.22.0
fastapi>=0.100.1
firebase-admin
